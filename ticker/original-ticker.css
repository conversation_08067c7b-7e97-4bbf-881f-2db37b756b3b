.background-color-dark {
  background-color: var(--deprecate--b900);
  color: whitesmoke;
}

.page-wrapper {
  overflow: hidden;
  font-family: "Robotoflex Variablefont";
}

.section_customer-logos {
  display: flex;
  overflow: hidden;
  flex-flow: column;
  align-items: center;
  background-color: var(--_tokens---surface--bg-canvas);
  font-family: "Robotoflex Variablefont";
  color: var(--white);
}

.section_customer-logos:where(.w-variant-8d1714ea-b666-152e-18f9-a27e1607acd8) {
  background-color: var(--_tokens---surface--bg-canvas-light);
}

.padding-ticker {
  position: relative;
  padding-top: 1rem;
  padding-bottom: 4rem;
  color: white;
}

.ticker_component {
  display: flex;
  width: auto;
  min-height: 6rem;
  line-height: 1;
}

.body-13 {
  font-family: "Robotoflex Variablefont";
}

@media screen and (min-width: 1280px) {
  .section_customer-logos {
    display: flex;
    flex-flow: column;
    align-items: center;
  }
}

@media screen and (max-width: 991px) {
  .padding-ticker {
    padding-top: 0.25rem;
  }
}

@media screen and (max-width: 991px) {
  .ticker_component {
    min-height: 3rem;
  }
}