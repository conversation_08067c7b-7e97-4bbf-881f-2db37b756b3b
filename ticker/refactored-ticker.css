.background-color-dark {
  background-color: var(--deprecate--b900);
  color: whitesmoke;
}

.page-wrapper {
  overflow: hidden;
  font-family: "Robotoflex Variablefont";
}

.section_customer-logos {
  display: flex;
  overflow: hidden;
  flex-flow: column;
  align-items: center;
  background-color: var(--_tokens---surface--bg-canvas);
  font-family: "Robotoflex Variablefont";
  color: var(--white);
}

.section_customer-logos:where(.w-variant-8d1714ea-b666-152e-18f9-a27e1607acd8) {
  background-color: var(--_tokens---surface--bg-canvas-light);
}

.padding-ticker {
  position: relative;
  padding-top: 1rem;
  padding-bottom: 4rem;
  color: white;
}

.body-13 {
  font-family: "Robotoflex Variablefont";
}

._2025-ticker-component {
  display: block;
  overflow: hidden;
  box-sizing: border-box;
  width: var(--size--max-content-width);
  height: var(--_tokens---ticker--ticker-height);
  min-height: var(--_tokens---ticker--ticker-height);
}

._2025-ticker-component.div-block-283 {
  display: flex;
  height: var(--_tokens---ticker--ticker-height);
  min-height: 6rem;
  padding-left: 0px;
  justify-content: center;
  align-items: center;
  vertical-align: baseline;
}

.div-block-283 {
  display: flex;
  overflow: hidden;
  width: 100%;
  min-height: var(--_tokens---ticker--ticker-height);
  align-items: center;
  animation: tickerscroll var(--ticker-duration) linear infinite;
}

.div-block-283._2025-ticker-component {}

@media screen and (min-width: 1280px) {
  .section_customer-logos {
    display: flex;
    flex-flow: column;
    align-items: center;
  }
}

@media screen and (max-width: 991px) {
  .padding-ticker {
    padding-top: 0.25rem;
  }
}