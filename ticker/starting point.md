
# Problem statement
we use webflow and their UI to build our site. On there is a scrolling ticker that is not smooth on mobile. We want to make it smooth. It must be in webflow and CSS only


## Original CSS

```css
.background-color-dark {
  background-color: var(--deprecate--b900);
  color: whitesmoke;
}

.page-wrapper {
  overflow: hidden;
  font-family: "Robotoflex Variablefont";
}

.section_customer-logos {
  display: flex;
  overflow: hidden;
  flex-flow: column;
  align-items: center;
  background-color: var(--_tokens---surface--bg-canvas);
  font-family: "Robotoflex Variablefont";
  color: var(--white);
}

.section_customer-logos:where(.w-variant-8d1714ea-b666-152e-18f9-a27e1607acd8) {
  background-color: var(--_tokens---surface--bg-canvas-light);
}

.padding-ticker {
  position: relative;
  padding-top: 1rem;
  padding-bottom: 4rem;
  color: white;
}

.ticker_component {
  display: flex;
  width: auto;
  min-height: 6rem;
  line-height: 1;
}

.body-13 {
  font-family: "Robotoflex Variablefont";
}

@media screen and (min-width: 1280px) {
  .section_customer-logos {
    display: flex;
    flex-flow: column;
    align-items: center;
  }
}

@media screen and (max-width: 991px) {
  .padding-ticker {
    padding-top: 0.25rem;
  }
}

@media screen and (max-width: 991px) {
  .ticker_component {
    min-height: 3rem;
  }
}
```


## Claude's Fix
Looking at your ticker implementation, I can identify several structural issues preventing the infinite scroll from working:

## Key Problems:

**1. Missing Layout Structure**
Your `._2025-ticker-runway` needs to be a flex container with proper child layout:

```css
._2025-ticker-runway {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: max-content; /* Critical: allows content to extend beyond container */
  animation: ticker-scroll 120s linear infinite;
  will-change: transform;
}
```

**2. Broken Animation Transform**
Your current transform has syntax errors:
```css
/* Current (broken) */
transform: translateX(calc(-50% - 3)); /* Missing unit, incomplete calc */

/* Should be */
transform: translateX(-50%);
```

**3. Logo Wrapper Layout Issues**
Your `.home-logo_wrapper` has conflicting flex properties:
```css
.home-logo_wrapper {
  display: flex;
  flex: 0 0 auto; /* Change from "0 1 auto" to prevent shrinking */
  white-space: nowrap; /* Prevent text wrapping */
  margin-right: 2rem; /* Add spacing between items */
}
```

## Complete Fix:

Add this to your code embed:

```html
<style>
@keyframes ticker-scroll {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

._2025-ticker-runway {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: max-content;
  animation: ticker-scroll 120s linear infinite;
  will-change: transform;
}

.home-logo_wrapper {
  flex: 0 0 auto;
  margin-right: 2rem;
  white-space: nowrap;
}
</style>
```

## Structure Requirements:
- **Parent** (`._2025-ticker-component`): `overflow: hidden` ✓
- **Child** (`._2025-ticker-runway`): `display: flex`, `width: max-content`
- **Items** (`.home-logo_wrapper`): `flex: 0 0 auto`, proper spacing

The fundamental issue is that without `width: max-content` on the runway, your content can't extend beyond the container width, making the transform ineffective.

## ChatGPT critiques Claude

Short answer: Partially correct, but it misses several fatal errors and over-simplifies the mechanics.

⸻

What the critique gets right

#	Point raised	Status	Notes
1	transform: translateX(calc(-50% - 3)) contains a unit-less “3”	✔	Any unitless length inside calc() is invalid; animation is dropped.
2	._2025-ticker-runway must be non-wrapping flex	✔	flex-wrap: nowrap (or white-space: nowrap on an inline element) is mandatory.
3	flex: 0 0 auto on each logo stops shrinkage	✔	Prevents flexbox from squeezing items as they leave the viewport.

These are necessary, but not sufficient.

⸻

Critical problems it misses

#	Problem	Consequence	Fix
A	Invalid selector – your embed uses \._2025-ticker-runway (note the back-slash).	Animation never attaches; Webflow copies the rule verbatim.	Delete the leading back-slash.
B	Unbalanced braces inside the <style> block – the closing } for @keyframes is inside a comment.	Whole stylesheet after that point is ignored.	Move the comment outside the block or close the brace before the comment.
C	@keyframes goes from 0 → -50 % but the track only contains one copy of the logos.	At 50 % progress everything has scrolled off and blanks are shown.	Duplicate the logo list inside the runway and translate by 100 % of that combined width (either -100% or -50% if you doubled the content).
D	width: max-content alone is not “critical”.  Any explicit width that exceeds the parent will work; the real requirement is simply overflow beyond the viewport.	The analysis confuses cause and effect.	Keep max-content if you like, but the essential part is that the total width exceeds the parent’s width.
E	display:flex + grid-* properties on .home-logo_wrapper	Grid declarations are ignored on flex items; harmless but dead code.	Delete the grid rules or convert the wrapper to display:grid if that’s what you want.
F	Repeated global utility rules (everything from .background-color-dark to .body-13) are copied into three different style blocks.	Rule duplication slows parsing and complicates debugging.	Keep global utilities in one global sheet; embed only the ticker-specific CSS.
G	No GPU-friendly property is animating.  You correctly set will-change:transform and translateZ(0), but the transform itself must be on the same element you animate.	If transform is only inside the keyframe, it’s fine.  If any intermediate rule re-defines transform, you drop to the CPU.	Verify that no other rule writes a transform on ._2025-ticker-runway.


⸻

Minimum working ticker (Webflow-safe)

<style>
/* Duplicate logo set in HTML: <div class="runway">…logo set…</div><div class="runway">…same set…</div> */

@keyframes ticker-scroll {
  from { transform: translateX(0); }
  to   { transform: translateX(-100%); } /* full width of two sets */
}

._2025-ticker-track {          /* formerly _2025-ticker-runway */
  display:flex;
  flex-wrap:nowrap;
  animation: ticker-scroll 120s linear infinite;
  will-change: transform;
  -webkit-transform:translateZ(0); /* forces composite layer */
}

.home-logo_wrapper {
  flex:0 0 auto;              /* don’t shrink */
  margin-right:2rem;          /* spacing */
  white-space:nowrap;
}
</style>

Place both .runway copies inside the same .track element; the animation runs on the track.

⸻

Mechanism of failure (why nothing moves)
	1.	Selector typo →  animation never starts.
	2.	Brace mismatch →  browser throws away everything after it, including the selector fix.
	3.	Unitless 3 →  even if the animation attached, it would be invalid and skipped.
	4.	Single logo set →  if the above were fixed, you would still see a blank gap halfway through the cycle.

Until all four are corrected the ticker will remain static.


## My Try

I tried Claude's but couldn't get it to work. I almost have it, the scrolling happens but very slowly and I don't know why it now scroll, what are redundant rules and how to speed it up.

### container CSS
```css
.background-color-dark {
  background-color: var(--deprecate--b900);
  color: whitesmoke;
}

.page-wrapper {
  overflow: hidden;
  font-family: "Robotoflex Variablefont";
}

.section_customer-logos {
  display: flex;
  overflow: hidden;
  flex-flow: column;
  align-items: center;
  background-color: var(--_tokens---surface--bg-canvas);
  font-family: "Robotoflex Variablefont";
  color: var(--white);
}

.section_customer-logos:where(.w-variant-8d1714ea-b666-152e-18f9-a27e1607acd8) {
  background-color: var(--_tokens---surface--bg-canvas-light);
}

.padding-ticker {
  position: relative;
  padding-top: 1rem;
  padding-bottom: 4rem;
  color: white;
}

.body-13 {
  font-family: "Robotoflex Variablefont";
}

._2025-ticker-component {
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
  width: auto;
  height: auto;
  min-height: 6rem;
  justify-content: center;
}

@media screen and (min-width: 1280px) {
  .section_customer-logos {
    display: flex;
    flex-flow: column;
    align-items: center;
  }
}

@media screen and (max-width: 991px) {
  .padding-ticker {
    padding-top: 0.25rem;
  }
}
```

### Ticker Track CSS
there are 2 ticker tracks and there is a code embed on the first one
```css
.background-color-dark {
  background-color: var(--deprecate--b900);
  color: whitesmoke;
}

.page-wrapper {
  overflow: hidden;
  font-family: "Robotoflex Variablefont";
}

.section_customer-logos {
  display: flex;
  overflow: hidden;
  flex-flow: column;
  align-items: center;
  background-color: var(--_tokens---surface--bg-canvas);
  font-family: "Robotoflex Variablefont";
  color: var(--white);
}

.section_customer-logos:where(.w-variant-8d1714ea-b666-152e-18f9-a27e1607acd8) {
  background-color: var(--_tokens---surface--bg-canvas-light);
}

.padding-ticker {
  position: relative;
  padding-top: 1rem;
  padding-bottom: 4rem;
  color: white;
}

.body-13 {
  font-family: "Robotoflex Variablefont";
}

._2025-ticker-runway {
  display: flex;
  height: 100%;
  max-width: var(--size--max-content-width);
  margin-right: auto;
  margin-left: auto;
  padding-left: 4rem;
  flex-flow: row;
  justify-content: flex-start;
  align-items: stretch;
  grid-auto-columns: 1fr;
  gap: 4rem;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  animation: 90s linear 0s infinite normal none running ticker-scroll;
  will-change: transform;
}

@media screen and (min-width: 1280px) {
  .section_customer-logos {
    display: flex;
    flex-flow: column;
    align-items: center;
  }
}

@media screen and (max-width: 991px) {
  .padding-ticker {
    padding-top: 0.25rem;
  }
}
```

the ticker track has a code embed under it with custom code
```html
<style>

@keyframes ticker-scroll {
  from { transform: translateX(0); }
  to   { transform: translateX(calc(-50% - 3rem)); }
}

._2025-ticker-track {        
  animation: ticker-scroll 90s linear infinite;
  will-change: transform;
  -webkit-transform:translateZ(0); 
}

</style>
```
children of the ticker track are a series of icon wrappers with CSS
```css
.background-color-dark {
  background-color: var(--deprecate--b900);
  color: whitesmoke;
}

.page-wrapper {
  overflow: hidden;
  font-family: "Robotoflex Variablefont";
}

.home-logo_wrapper {
  display: flex;
  height: 2.5rem;
  justify-content: center;
  place-items: center stretch;
  align-self: auto;
  flex: 0 1 auto;
  grid-auto-columns: 1fr;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
}

.section_customer-logos {
  display: flex;
  overflow: hidden;
  flex-flow: column;
  align-items: center;
  background-color: var(--_tokens---surface--bg-canvas);
  font-family: "Robotoflex Variablefont";
  color: var(--white);
}

.section_customer-logos:where(.w-variant-8d1714ea-b666-152e-18f9-a27e1607acd8) {
  background-color: var(--_tokens---surface--bg-canvas-light);
}

.padding-ticker {
  position: relative;
  padding-top: 1rem;
  padding-bottom: 4rem;
  color: white;
}

.body-13 {
  font-family: "Robotoflex Variablefont";
}

@media screen and (min-width: 1280px) {
  .section_customer-logos {
    display: flex;
    flex-flow: column;
    align-items: center;
  }
}

@media screen and (max-width: 991px) {
  .home-logo_wrapper {
    height: 2rem;
  }
}

@media screen and (max-width: 991px) {
  .padding-ticker {
    padding-top: 0.25rem;
  }
}

@media screen and (max-width: 767px) {
  .home-logo_wrapper {
    padding-bottom: 0rem;
  }
}

@media screen and (max-width: 479px) {
  .home-logo_wrapper {
    height: 1.2rem;
    justify-content: center;
    align-items: flex-start;
  }
}
```