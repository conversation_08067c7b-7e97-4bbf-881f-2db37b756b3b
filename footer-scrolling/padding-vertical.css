.padding-xxlarge {
  padding: 5rem;
}

.padding-vertical {
  padding-right: 0rem;
  padding-left: 0rem;
}

.padding-vertical.padding-xxlarge {}

.background-color-dark {
  background-color: var(--deprecate--b900);
  color: whitesmoke;
}

.page-wrapper {
  overflow: hidden;
  font-family: "Robotoflex Variablefont";
}

@media screen and (max-width: 991px) {
  .padding-xxlarge {
    padding: 4rem;
  }
}

@media screen and (max-width: 991px) {
  .padding-vertical {
    padding-right: 0rem;
    padding-left: 0rem;
  }
}

@media screen and (max-width: 767px) {
  .padding-xxlarge {
    padding: 3rem;
  }
}

@media screen and (max-width: 767px) {
  .padding-vertical {
    padding-right: 0rem;
    padding-left: 0rem;
  }
}

@media screen and (max-width: 479px) {
  .padding-vertical {
    padding-right: 0rem;
    padding-left: 0rem;
  }
}