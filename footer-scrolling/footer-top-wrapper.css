.background-color-dark {
  background-color: var(--deprecate--b900);
  color: whitesmoke;
}

.page-wrapper {
  overflow: hidden;
  font-family: "Robotoflex Variablefont";
}

.footer_top-wrapper {
  align-items: start;
  gap: 1rem 2rem;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: auto;
}

@media screen and (max-width: 991px) {
  .footer_top-wrapper {
    row-gap: 3rem;
    grid-template-columns: 1fr 1fr 1fr;
  }
}

@media screen and (max-width: 767px) {
  .footer_top-wrapper {
    row-gap: 2.5rem;
  }
}

@media screen and (max-width: 479px) {
  .footer_top-wrapper {
    row-gap: 2.5rem;
    grid-template-columns: 1fr;
  }
}