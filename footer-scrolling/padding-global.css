.padding-global {
  min-height: auto;
  padding-right: 3rem;
  padding-left: 3rem;
}

.padding-global:where(.w-variant-fe1058a6-cb37-2d23-8510-27ffbbe19435) {
  background-color: var(--_tokens---surface--bg-canvas);
}

.background-color-dark {
  background-color: var(--deprecate--b900);
  color: whitesmoke;
}

.page-wrapper {
  overflow: hidden;
  font-family: "Robotoflex Variablefont";
}

@media screen and (min-width: 1280px) {
  .padding-global:where(.w-variant-fe1058a6-cb37-2d23-8510-27ffbbe19435) {
    background-color: var(--_tokens---surface--bg-canvas);
  }
}

@media screen and (max-width: 767px) {
  .padding-global {
    padding-right: 1.25rem;
    padding-left: 1.25rem;
  }
}